<?php

/**
 * Casette Page - Masseria Torricella
 * Converted from HTML using template system
 */

// Initialize template system
require_once '../includes/init.php';

// Page configuration
$page_config = [
  'page_title' => 'Masseria Torricella',
  'page_description' => '',
  'page_keywords' => '',
  'current_page' => 'casette.htm'
];

// Content function
function renderContent()
{
  $current_lang = getCurrentLanguage();
  $asset_path = getAssetPath($current_lang);
?>

  <div class="slider">
    <div class="container">
      <div class="row">
        <div class="span10 offset1">
          <div class="flexslider2">
            <ul class="slides">
              <li data-thumb="">
                <img src="../assets/img/slider/camere2.jpg">
                <p class="flex-caption">THE QUERCIA COTTAGES</p>
              </li>
              <li data-thumb="">
                <img src="../assets/img/slider/casette.jpg">
                <p class="flex-caption">THE QUERCIA COTTAGES</p>
              </li>

            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- descrizione -->
  <div class="about-us container">
    <div class="row">
      <div class="services-half-width-text span12">
        <h3>Hospitality: <strong>THE QUERCIA COTTAGES</strong></h3>
        <br>
        <p align="justify">At about two hundred meters, under the shadow of a centuries old oak, there are <strong>THE QUERCIA COTTAGES,</strong> two independent apartments with kitchen and anything you may need for a completely independent holiday, studio apartments with: <br>
          <strong>- <strong>double bed</strong>,</strong><br>
          <strong>- single sofa-bed,</strong> <br>
          <strong>- corner kitchen-dining room;</strong><br>
          <br>
          The studios are equipped with air conditioning and have a nice veranda:<br>
          <strong>- private facilities,</strong><br>
          <strong>- TV,</strong><br>
          <strong>- hairdryer,</strong><br>
          <strong>- air conditioning and heating</strong><br>
          <strong>- cleaning service and daily
            towels and linen change</strong> .<br>

        </p>
      </div>
    </div>
  </div>

  <!-- gallery -->
  <div class="portfolio portfolio-page container">
    <div class="row">
      <div class="portfolio-navigator span12">
        <div class="portfolio-title">
          <h3>See photos of the cottages</h3>
        </div>
      </div>
    </div>
    <div class="row">
      <ul class="portfolio-img">
        <li data-id="p-1" data-type="camere" class="span3">
          <div class="work"> <a href="../assets/img/portfolio/casette4.jpg" rel="prettyPhoto[pp_gal]" title="le casette della quercia: panorama"> <img src="../assets/img/portfolio/casette4-p.jpg" alt=""> </a>
            <h4> panorama</h4>
          </div>
        </li>
        <li data-id="p-5" data-type="trullo" class="span3">
          <div class="work"> <a href="../assets/img/portfolio/casette3.jpg" rel="prettyPhoto[pp_gal]" title="le casette della quercia: la quercia"> <img src="../assets/img/portfolio/casette3-p.jpg" alt=""> </a>
            <h4>quercia</h4>
          </div>
        </li>
        <li data-id="p-12" data-type="dependance" class="span3">
          <div class="work"> <a href="../assets/img/portfolio/casette1.jpg" rel="prettyPhoto[pp_gal]" title="le casette della quercia: zona giorno"> <img src="../assets/img/portfolio/casette1-p.jpg" alt=""> </a>
            <h4>Inside </h4>
          </div>
        </li>
        <li data-id="p-12" data-type="dependance" class="span3">
          <div class="work"> <a href="../assets/img/portfolio/casette2.jpg" rel="prettyPhoto[pp_gal]" title="le casette della quercia: zona notte"> <img src="../assets/img/portfolio/casette2-p.jpg" alt=""> </a>
            <h4>Inside </h4>
          </div>
        </li>

      </ul>
    </div>
  </div>
  <!-- Call To Action -->
  <div class="call-to-action container">
    <div class="row">
      <div class="call-to-action-text span12">
        <div class="ca-text">
          <p>Daily room rates</p>
        </div>
        <div class="ca-button"><a href="../prenotazioni.php">Booking </a></div>

      </div>
    </div>

    <div id="slope-bl" data-id="8c73d3c9-df98-41f2-8b24-618e83ff232a" data-lang="en"> </div>

  </div>
  <div class="portfolio portfolio-page container">
    <div class="row">
      <div class="portfolio-navigator span12">
        <div class="portfolio-title">
          <h3> Types of rooms</h3>
        </div>
      </div>
    </div>
    <div class="row">
      <ul class="portfolio-img">
        <li data-id="p-1" data-type="camere" class="span3">
          <div class="work"> <img src="../assets/img/portfolio/camera1-p.jpg" alt="">
            <h4>Rooms in the farmhouse </h4>
            <p><a href="camere1.php">CHARACTERISTICS ></a></p>
          </div>
        </li>
        <li data-id="p-5" data-type="trullo" class="span3">
          <div class="work"> <img src="../assets/img/portfolio/trullo1-p.jpg" alt="">
            <h4>the "trullo"</h4>
            <p><a href="trullo.php">CHARACTERISTICS ></a></p>
          </div>
        </li>
        <li data-id="p-12" data-type="dependance" class="span3">
          <div class="work"> <img src="../assets/img/portfolio/dependance1-p.jpg" alt="">
            <h4>The guest houses</h4>
            <p><a href="dependance.php">CHARACTERISTICS ></a></p>
          </div>
        </li>
        <li data-id="p-11" data-type="casette" class="span3">
          <div class="work"> <img src="../assets/img/portfolio/casette4-p.jpg" alt="">
            <h4>The Quercia cottages </h4>
            <p><a href="casette.php">CHARACTERISTICS ></a></p>
          </div>
        </li>
      </ul>
    </div>
  </div>
  <!-- Footer -->

  <!-- Javascript -->

<?php
}

// Render the complete page
renderPage('renderContent', $page_config);
?>