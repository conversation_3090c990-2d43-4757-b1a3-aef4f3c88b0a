<?php
/**
 * Script per verificare che tutti i percorsi asset siano corretti
 * nelle sottocartelle delle lingue
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

echo "🔍 VERIFICA PERCORSI ASSET\n";
echo "==========================\n\n";

/**
 * Verifica i percorsi asset in un file PHP
 */
function verifyAssetPaths($file_path) {
    if (!file_exists($file_path)) {
        return false;
    }
    
    $content = file_get_contents($file_path);
    
    // Determina se il file è in una sottocartella
    $dir_parts = array_filter(explode('/', dirname($file_path)));
    $is_in_subdirectory = count($dir_parts) > 0;
    
    $issues = [];
    
    if ($is_in_subdirectory) {
        // Per file in sottocartelle, cerca percorsi che dovrebbero iniziare con ../
        $problematic_patterns = [
            'src="assets/' => 'src="assets/',
            'href="assets/' => 'href="assets/',
            'data-thumb="assets/' => 'data-thumb="assets/',
            'data-src="assets/' => 'data-src="assets/',
            'url(assets/' => 'url(assets/'
        ];
        
        foreach ($problematic_patterns as $pattern => $description) {
            if (preg_match_all('#' . preg_quote($pattern, '#') . '#i', $content, $matches)) {
                $issues[] = [
                    'type' => 'missing_relative_path',
                    'pattern' => $pattern,
                    'count' => count($matches[0]),
                    'description' => "Percorsi che dovrebbero iniziare con ../"
                ];
            }
        }
    }
    
    // Cerca percorsi corretti
    $correct_patterns = [
        'src="../' => 'Immagini con percorso corretto',
        'href="../' => 'CSS/Link con percorso corretto',
        'data-thumb="../' => 'Thumbnail con percorso corretto',
        'data-src="../' => 'Data-src con percorso corretto'
    ];
    
    $correct_counts = [];
    foreach ($correct_patterns as $pattern => $description) {
        if (preg_match_all('#' . preg_quote($pattern, '#') . '#i', $content, $matches)) {
            $correct_counts[$description] = count($matches[0]);
        }
    }
    
    return [
        'issues' => $issues,
        'correct_counts' => $correct_counts,
        'is_in_subdirectory' => $is_in_subdirectory
    ];
}

// Verifica tutte le cartelle delle lingue
$language_dirs = ['eng', 'deu', 'spa', 'fra'];
$total_files = 0;
$total_issues = 0;
$files_with_issues = 0;

foreach ($language_dirs as $lang_dir) {
    if (!is_dir($lang_dir)) {
        echo "⚠️  Cartella $lang_dir non trovata, saltando...\n";
        continue;
    }
    
    echo "📁 Verificando cartella: $lang_dir\n";
    echo str_repeat("-", 30) . "\n";
    
    $php_files = glob("$lang_dir/*.php");
    
    if (empty($php_files)) {
        echo "ℹ️  Nessun file PHP trovato in $lang_dir\n\n";
        continue;
    }
    
    $dir_files = 0;
    $dir_issues = 0;
    $dir_files_with_issues = 0;
    $dir_correct_total = 0;
    
    foreach ($php_files as $php_file) {
        $filename = basename($php_file);
        $total_files++;
        $dir_files++;
        
        $result = verifyAssetPaths($php_file);
        
        if ($result && !empty($result['issues'])) {
            echo "   ❌ $filename:\n";
            foreach ($result['issues'] as $issue) {
                echo "      - {$issue['count']} percorsi con pattern '{$issue['pattern']}'\n";
                $total_issues += $issue['count'];
                $dir_issues += $issue['count'];
            }
            $files_with_issues++;
            $dir_files_with_issues++;
        } else {
            $correct_count = array_sum($result['correct_counts'] ?? []);
            if ($correct_count > 0) {
                echo "   ✅ $filename: $correct_count percorsi corretti\n";
            } else {
                echo "   ℹ️  $filename: nessun asset trovato\n";
            }
            $dir_correct_total += $correct_count;
        }
    }
    
    echo "\n📊 Risultati per $lang_dir:\n";
    echo "   📄 File verificati: $dir_files\n";
    echo "   ❌ File con problemi: $dir_files_with_issues\n";
    echo "   🔧 Percorsi problematici: $dir_issues\n";
    echo "   ✅ Percorsi corretti: $dir_correct_total\n\n";
}

echo "🎉 RIEPILOGO FINALE\n";
echo "===================\n";
echo "File verificati: $total_files\n";
echo "File con problemi: $files_with_issues\n";
echo "Percorsi problematici: $total_issues\n";

if ($total_issues === 0) {
    echo "\n✅ VERIFICA SUPERATA!\n";
    echo "💡 Tutti i percorsi asset nelle sottocartelle sono corretti.\n";
    echo "🔗 I file usano correttamente '../assets/' per i percorsi relativi.\n";
    echo "🖼️  Attributi verificati: src, href, data-thumb, data-src, url()\n";
} else {
    echo "\n⚠️  Alcuni percorsi necessitano ancora correzione.\n";
    echo "💡 Esegui 'php tools/fix-asset-paths.php' per correggerli.\n";
}
?>
