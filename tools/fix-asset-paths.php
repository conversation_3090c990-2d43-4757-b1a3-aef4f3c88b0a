<?php

/**
 * Script per correggere i percorsi degli asset nei file già convertiti
 * Specificamente per i file nelle sottocartelle delle lingue
 */

// Cambia directory di lavoro alla root del sito
chdir(dirname(__DIR__));

echo "🔧 CORREZIONE PERCORSI ASSET\n";
echo "============================\n\n";

/**
 * Corregge i percorsi degli asset in un file PHP
 */
function fixAssetPaths($file_path)
{
    if (!file_exists($file_path)) {
        return false;
    }

    $content = file_get_contents($file_path);
    $original_content = $content;

    // Determina il numero di livelli di directory
    $dir_parts = array_filter(explode('/', dirname($file_path)));
    $dir_levels = count($dir_parts);

    if ($dir_levels === 0) {
        return false; // File nella root, non serve correzione
    }

    $back_to_root = str_repeat('../', $dir_levels);

    // Pattern per trovare percorsi asset che non sono ancora corretti
    $patterns = [
        // src="assets/..." -> src="../assets/..."
        '#src=["\'](?!https?://|//|\.\./|/)([^"\']*?)(["\'])#i',
        // href="assets/..." -> href="../assets/..."
        '#href=["\'](?!https?://|//|mailto:|tel:|\.\./|/)([^"\']*?)(["\'])#i',
        // data-thumb="assets/..." -> data-thumb="../assets/..."
        '#data-thumb=["\'](?!https?://|//|\.\./|/)([^"\']*?)(["\'])#i',
        // data-src="assets/..." -> data-src="../assets/..."
        '#data-src=["\'](?!https?://|//|\.\./|/)([^"\']*?)(["\'])#i',
        // url(assets/...) -> url(../assets/...)
        '#url\(["\']?(?!https?://|//|\.\./|/)([^"\']*?)\)#i'
    ];

    $changes_made = 0;

    foreach ($patterns as $pattern) {
        $content = preg_replace_callback($pattern, function ($matches) use ($back_to_root, &$changes_made) {
            $path = $matches[1];
            $quote = isset($matches[2]) ? $matches[2] : '';

            // Skip se il percorso è già corretto o non è relativo
            if (
                empty($path) ||
                strpos($path, '../') === 0 ||
                strpos($path, '/') === 0 ||
                strpos($path, 'http') === 0 ||
                strpos($path, 'mailto:') === 0 ||
                strpos($path, 'tel:') === 0 ||
                strpos($path, '$') !== false ||
                strpos($path, '<?') !== false
            ) {
                return $matches[0];
            }

            // Aggiungi il prefisso per tornare alla root
            $new_path = $back_to_root . $path;
            $changes_made++;

            // Ricostruisci l'attributo
            if (strpos($matches[0], 'url(') !== false) {
                return 'url(' . $new_path . ')';
            } else {
                $attr_name = substr($matches[0], 0, strpos($matches[0], '=') + 1);
                return $attr_name . '"' . $new_path . '"';
            }
        }, $content);
    }

    // Salva solo se sono state fatte modifiche
    if ($content !== $original_content && $changes_made > 0) {
        file_put_contents($file_path, $content);
        return $changes_made;
    }

    return 0;
}

// Trova tutti i file PHP nelle sottocartelle delle lingue
$language_dirs = ['eng', 'deu', 'spa', 'fra'];
$total_files = 0;
$total_changes = 0;
$files_modified = 0;

foreach ($language_dirs as $lang_dir) {
    if (!is_dir($lang_dir)) {
        echo "⚠️  Cartella $lang_dir non trovata, saltando...\n";
        continue;
    }

    echo "📁 Processando cartella: $lang_dir\n";
    echo str_repeat("-", 30) . "\n";

    $php_files = glob("$lang_dir/*.php");

    if (empty($php_files)) {
        echo "ℹ️  Nessun file PHP trovato in $lang_dir\n\n";
        continue;
    }

    $dir_files = 0;
    $dir_changes = 0;
    $dir_modified = 0;

    foreach ($php_files as $php_file) {
        $filename = basename($php_file);
        $total_files++;
        $dir_files++;

        $changes = fixAssetPaths($php_file);

        if ($changes > 0) {
            echo "   ✅ $filename: $changes percorsi corretti\n";
            $total_changes += $changes;
            $dir_changes += $changes;
            $files_modified++;
            $dir_modified++;
        } else {
            echo "   ℹ️  $filename: nessuna correzione necessaria\n";
        }
    }

    echo "\n📊 Risultati per $lang_dir:\n";
    echo "   📄 File processati: $dir_files\n";
    echo "   📝 File modificati: $dir_modified\n";
    echo "   🔧 Percorsi corretti: $dir_changes\n\n";
}

echo "🎉 RIEPILOGO FINALE\n";
echo "===================\n";
echo "File processati: $total_files\n";
echo "File modificati: $files_modified\n";
echo "Percorsi corretti: $total_changes\n";

if ($total_changes > 0) {
    echo "\n✅ Correzione completata!\n";
    echo "💡 Tutti i percorsi asset nelle sottocartelle sono ora corretti.\n";
    echo "🔗 I file ora usano '../assets/' invece di 'assets/'\n";
} else {
    echo "\n✅ Nessuna correzione necessaria!\n";
    echo "💡 Tutti i percorsi asset sono già corretti.\n";
}
